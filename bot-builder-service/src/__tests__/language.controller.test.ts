import { Request, Response } from "express";
import { LanguageController } from "../controllers/language.controller";
import { Models } from "@neuratalk/bot-store";
import { AppContext } from "../types/context.types";

jest.mock("@neuratalk/common", () => ({
  getPaginatedResults: jest.fn(),
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
  successResponse: jest.fn((data) => ({ success: true, data, timestamp: new Date() })),
  errorResponse: jest.fn((error) => ({
    success: false,
    error: { code: error.code, message: error.message },
    timestamp: new Date(),
  })),
}));

const { getPaginatedResults, successResponse, errorResponse } = require("@neuratalk/common");

describe("LanguageController", () => {
  let controller: LanguageController;
  let mockModels: Models;
  let mockReq: Partial<Request>;
  let mockRes: Partial<Response>;
  let mockContext: AppContext;

  beforeEach(() => {
    mockModels = {
      Language: {
        create: jest.fn(),
        findOne: jest.fn(),
        findByPk: jest.fn(),
      },
      Bot: {},
      BotLanguage: {},
      FaqCategory: {},
      FaqItems: {},
      FaqTranslation: {},
      Flow: {},
      IntentItems: {},
      IntentUtterance: {},
      UtteranceTranslation: {},
      Entities: {},
      BotModel: {},
    } as unknown as Models;

    mockContext = {
      db: {
        models: mockModels,
        getSequelize: jest.fn(),
        connect: jest.fn(),
        disconnect: jest.fn(),
        transaction: jest.fn((cb) => cb({} as any)),
        sequelize: {} as any,
        healthCheck: jest.fn(),
      } as any,
      botService: {} as any,
      flowService: {} as any,
      buildService: {} as any,
    };

    controller = new LanguageController(mockContext);

    mockReq = {
      body: {},
      params: {},
      query: {},
      user: { id: "user-123" },
    };

    mockRes = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn().mockReturnThis(),
      send: jest.fn().mockReturnThis(),
    };

    jest.clearAllMocks();
  });

  describe("create", () => {
    it("should create a language successfully", async () => {
      const mockLanguage = { id: "lang-123", name: "English", code: "en" };
      mockReq.body = { name: "English", code: "en" };
      (mockModels.Language.create as jest.Mock).mockResolvedValue(mockLanguage);

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockModels.Language.create).toHaveBeenCalledWith(mockReq.body);
      expect(mockRes.status).toHaveBeenCalledWith(201);
      expect(successResponse).toHaveBeenCalledWith(mockLanguage);
    });

    it("should handle creation error", async () => {
      mockReq.body = { name: "English", code: "en" };
      (mockModels.Language.create as jest.Mock).mockRejectedValue(new Error("Creation failed"));

      await controller.create(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(400);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "VALIDATION_ERROR",
      });
    });
  });

  describe("getAll", () => {
    it("should retrieve all languages", async () => {
      const mockResult = {
        items: [{ id: "lang-1" }],
        pagination: { page: 1, limit: 20, total: 1, totalPages: 1, hasNext: false, hasPrev: false },
      };
      (getPaginatedResults as jest.Mock).mockResolvedValue(mockResult);

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(getPaginatedResults).toHaveBeenCalledWith(mockModels.Language, mockReq.query, [
        "name",
        "code",
      ]);
      expect(successResponse).toHaveBeenCalledWith(mockResult);
    });

    it("should handle getAll error", async () => {
      (getPaginatedResults as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getAll(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch languages",
      });
    });
  });

  describe("getById", () => {
    it("should retrieve language by ID", async () => {
      const mockLanguage = { id: "lang-123", name: "English", code: "en" };
      mockReq.params = { id: "lang-123" };
      (mockModels.Language.findOne as jest.Mock).mockResolvedValue(mockLanguage);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockModels.Language.findOne).toHaveBeenCalledWith({ where: { id: "lang-123" } });
      expect(successResponse).toHaveBeenCalledWith(mockLanguage);
    });

    it("should return 404 for non-existent language", async () => {
      mockReq.params = { id: "non-existent" };
      (mockModels.Language.findOne as jest.Mock).mockResolvedValue(null);

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(404);
      expect(errorResponse).toHaveBeenCalledWith({
        code: "NOT_FOUND",
        message: "Language not found",
      });
    });

    it("should handle getById error", async () => {
      mockReq.params = { id: "lang-123" };
      (mockModels.Language.findOne as jest.Mock).mockRejectedValue(new Error("Database error"));

      await controller.getById(mockReq as any, mockRes as Response);

      expect(mockRes.status).toHaveBeenCalledWith(500);
      expect(errorResponse).toHaveBeenCalledWith({
        error: expect.any(Error),
        code: "INTERNAL_ERROR",
        message: "Failed to fetch language",
      });
    });
  });
});
