/**
 * Bot Builder Service Application
 *
 * Main application setup for the bot-builder-service.
 */

import express, { Application, Request, Response, NextFunction, Router } from "express";
import cors from "cors";
import helmet from "helmet";
import compression from "compression";
import morgan from "morgan";
import swaggerUi from "swagger-ui-express";
import swaggerSpec from "./config/swagger";
import { ApiResponse, logger, KafkaProducer, KafkaConsumer } from "@neuratalk/common";

import config from "./config";
import { DatabaseConnection } from "@neuratalk/bot-store";
import { AppContext } from "./types/context.types";
import { createRoutes } from "./routers/index.router";
import { BotService } from "./services/bot.service";
import { FlowService } from "./services/flow.service";
import { TrainingResultHandler } from "./handlers/training-result.handler";
import { BuildService } from "./services/build.service";
import { authMiddleware } from "./middleware/auth.middleware";

export class App {
  public app: Application;
  private context!: AppContext;
  private kafkaProducer: KafkaProducer;
  private trainingResultConsumer: KafkaConsumer;
  private trainingResultHandler!: TrainingResultHandler;

  constructor() {
    this.app = express();
    const kafkaBrokers = config.kafka.brokers;
    this.kafkaProducer = new KafkaProducer({
      clientId: "bot-builder-service-producer",
      brokers: kafkaBrokers,
    });
    this.trainingResultConsumer = new KafkaConsumer({
      groupId: "bot-builder-result-group",
      brokers: kafkaBrokers,
    });
  }

  private async initializeCoreServices(): Promise<void> {
    const db = new DatabaseConnection();
    await db.connect();

    const flowService = new FlowService(db);
    const botService = new BotService(flowService, db);
    const buildService = new BuildService(db, this.kafkaProducer);
    this.trainingResultHandler = new TrainingResultHandler(buildService);

    this.context = {
      db,
      botService,
      flowService,
      buildService,
    };

    logger.info("Core services initialized.");
  }

  private async initializeKafka(): Promise<void> {
    await this.kafkaProducer.connect();
    await this.trainingResultConsumer.connect();
    await this.trainingResultConsumer.subscribeAndRun(
      "training-results",
      this.trainingResultHandler.handleMessage,
    );
  }
  private initializeMiddleware(): void {
    // Security middleware
    this.app.use(helmet());

    //TODO: enable it after deployment
    // CORS configuration
    // this.app.use(
    //   cors({
    //     origin: config.server.corsOrigins,
    //     credentials: true,
    //     methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    //     allowedHeaders: ["Content-Type", "Authorization"],
    //   })
    // );
    this.app.use(cors());

    // Compression
    this.app.use(compression());

    // Request parsing
    this.app.use(express.json({ limit: "10mb" }));
    this.app.use(express.urlencoded({ extended: true, limit: "10mb" }));

    // Logging
    if (config.server.env !== "test") {
      this.app.use(
        morgan("combined", {
          stream: {
            write: (message: string) => {
              logger.info(message.trim());
            },
          },
        }),
      );
    }

    // Request ID middleware
    this.app.use((req: Request, res: Response, next: NextFunction) => {
      const requestId =
        (req.headers["x-request-id"] as string) ||
        `req_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      (req as any).requestId = requestId;
      res.setHeader("X-Request-ID", requestId);
      next();
    });
  }

  private initializeRoutes(): void {
    // Swagger documentation
    this.app.use(
      "/api-docs",
      ...swaggerUi.serve,
      swaggerUi.setup(swaggerSpec, {
        explorer: true,
        swaggerOptions: {
          persistAuthorization: true,
          docExpansion: "list",
        },
      }),
    );

    // Swagger JSON endpoint
    this.app.get("/api-docs.json", (req: Request, res: Response) => {
      res.setHeader("Content-Type", "application/json");
      res.send(swaggerSpec);
    });

    // Health check
    this.app.get("/health", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          status: "healthy",
          service: "bot-builder-service",
          version: process.env.npm_package_version || "1.0.0",
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    //TODO: need to rm authMiddleware from here if not required for all routes
    this.app.use("/api/v1", authMiddleware, createRoutes(this.context));

    // Root endpoint
    this.app.get("/", (req: Request, res: Response) => {
      res.json({
        success: true,
        data: {
          service: "bot-builder-service",
          version: process.env.npm_package_version || "1.0.0",
          environment: config.server.env,
          documentation: "/api-docs",
          endpoints: {
            health: "/health",
            swagger_ui: "/api-docs",
            swagger_json: "/api-docs.json",
            api_base: "/api/v1",
          },
          timestamp: new Date(),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler (must be last route)
    this.app.use("*", (req: Request, res: Response) => {
      res.status(404).json({
        success: false,
        error: {
          code: "NOT_FOUND",
          message: `Route ${req.method} ${req.originalUrl} not found`,
        },
        timestamp: new Date(),
      } as ApiResponse);
    });

    // Global error handler
    this.app.use((error: Error, req: Request, res: Response, next: NextFunction) => {
      logger.error("Unhandled error:", {
        error: error.message,
        stack: error.stack,
        requestId: (req as any).requestId,
        method: req.method,
        path: req.path,
      });

      const message =
        config.server.env === "production" ? "An internal error occurred" : error.message;

      res.status(500).json({
        success: false,
        error: {
          code: "INTERNAL_ERROR",
          message,
          ...(config.server.env !== "production" && { stack: error.stack }),
        },
        timestamp: new Date(),
      } as ApiResponse);
    });
  }

  public async start(): Promise<void> {
    try {
      // Initialize services, controllers, and routes
      await this.initializeCoreServices();
      this.initializeMiddleware();
      this.initializeRoutes();
      this.initializeErrorHandling();

      this.initializeKafka();

      // Start server
      const port = config.server.port;
      this.app.listen(port, () => {
        logger.info(`Bot Builder Service started on port ${port}`);
        logger.info(`Environment: ${config.server.env}`);
        logger.info(`Health check: http://localhost:${port}/health`);
      });
    } catch (error) {
      logger.error("Failed to start application:", error);
      process.exit(1);
    }
  }

  public async stop(): Promise<void> {
    try {
      logger.info("Shutting down Bot Builder Service...");
      await this.trainingResultConsumer.disconnect();
      await this.kafkaProducer.disconnect();
      await this.context.db.disconnect();
      logger.info("Bot Builder Service stopped");
    } catch (error) {
      logger.error("Error during shutdown:", error);
    }
  }
}
