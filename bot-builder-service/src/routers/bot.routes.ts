import { Router } from "express";
import { AppContext } from "../types/context.types";
import { BotController } from "../controllers/bot.controller";
import {
  validateBody,
  validateParams,
  validateQuery,
  PaginationQuerySchema,
} from "@neuratalk/common";
import {
  CreateBotRequestSchema,
  UpdateBotRequestSchema,
  BotIdParamSchema,
  BotChannelParamSchema,
  CreateChannelIntegrationSchema,
  BotChannelIdParamSchema,
  UpdateChannelIntegrationSchema,
} from "../schemas";

export function createBotRoutes(context: AppContext): Router {
  const router = Router();
  const botController = new BotController(context);

  router.post("/bots", validateBody(CreateBotRequestSchema), botController.createBot);
  router.get("/bots/:botId", validateParams(BotIdParamSchema), botController.getBotById);
  router.put(
    "/bots/:botId",
    validateParams(BotIdParamSchema),
    validateBody(UpdateBotRequestSchema),
    botController.updateBot,
  );
  router.delete("/bots/:botId", validateParams(BotIdParamSchema), botController.deleteBot);
  router.get("/bots", validateQuery(PaginationQuerySchema), botController.getBots);
  router.post("/bots/:botId/activate", validateParams(BotIdParamSchema), botController.activateBot);
  router.post(
    "/bots/:botId/deactivate",
    validateParams(BotIdParamSchema),
    botController.deactivateBot,
  );


  router.post("/bots/:botId/clone", validateParams(BotIdParamSchema), botController.cloneBot);
  router.post("/bots/:botId/export", validateParams(BotIdParamSchema), botController.exportBot);
  router.post("/bots/import", botController.importBot);


  // Channel routes
  router.get(
    "/bots/:botId/channels/:channelType",
    validateParams(BotChannelParamSchema),
    botController.getChannelConfig,
  );
  router.post(
    "/bots/:botId/channels",
    validateParams(BotIdParamSchema),
    validateBody(CreateChannelIntegrationSchema),
    botController.createChannelIntegration,
  );
  router.put(
    "/bots/:botId/channels/:channelId",
    validateParams(BotChannelIdParamSchema),
    validateBody(UpdateChannelIntegrationSchema),
    botController.updateChannelIntegration,
  );

  router.post("/bots/:botId/publish", validateParams(BotIdParamSchema), botController.publishBot);

  return router;
}