import { Router } from "express";
import { AppContext } from "../types/context.types";
import { FlowController } from "../controllers/flow.controller";
import { validateBody, validateParams, validateQuery } from "@neuratalk/common";
import {
  FlowIdParamSchema,
  UpdateFlowRequestSchema,
  FlowAppIdParamSchema,
  GetFlowsQuerySchema,
  BotIdParamSchema,
  FlowBotIdParamSchema,
  BulkCreateFlowsRequestSchema,
  CreateFlowPayloadSchema,
} from "../schemas";

export function createFlowRoutes(context: AppContext): Router {
  const router = Router();
  const flowController = new FlowController(context);

  router.post("/flows", validateBody(CreateFlowPayloadSchema), flowController.createFlow);
  router.get("/flows/:id", validateParams(FlowIdParamSchema), flowController.getFlowById);
  router.put(
    "/flows/:id",
    validateParams(FlowIdParamSchema),
    validateBody(UpdateFlowRequestSchema),
    flowController.updateFlow,
  );
  router.delete(
    "/flows/:id/apps/:appId",
    validateParams(FlowAppIdParamSchema),
    flowController.deleteFlow,
  );
  router.get(
    "/bots/:botId/flows",
    validateParams(FlowBotIdParamSchema),
    validateQuery(GetFlowsQuerySchema),
    flowController.getFlowsByBot,
  );

  router.post(
    "/flows/:id/clone",
    validateParams(FlowIdParamSchema),
    flowController.cloneFlow,
  );

  return router;
}