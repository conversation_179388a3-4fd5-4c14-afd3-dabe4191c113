import { Router } from "express";
import { AppContext } from "../types/context.types";

export function createDebuggerRoutes(context: AppContext): Router {
  const router = Router();
  const { debuggerService } = context;

  /**
   * @swagger
   * /debugger/stream/{conversationId}:
   *   get:
   *     summary: Stream debugger events via Server-Sent Events (SSE) for a specific conversation
   *     description: Establishes an SSE connection to receive real-time debugger logs, NLU details, and context objects for a given conversation ID.
   *     tags: [Debugger]
   *     parameters:
   *       - in: path
   *         name: conversationId
   *         required: true
   *         schema:
   *           type: string
   *           format: uuid
   *         description: The ID of the conversation to stream debugger events for.
   *     produces:
   *       - text/event-stream
   *     responses:
   *       200:
   *         description: SSE connection established. Events will be streamed.
   *         headers:
   *           Content-Type:
   *             schema:
   *               type: string
   *               example: text/event-stream
   *           Cache-Control:
   *             schema:
   *               type: string
   *               example: no-cache
   *           Connection:
   *             schema:
   *               type: string
   *               example: keep-alive
   */
  router.get("/debugger/stream/:conversationId", (req, res) => {
    const { conversationId } = req.params;

    res.writeHead(200, {
      "Content-Type": "text/event-stream",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    });

    // Send a heartbeat to keep the connection alive
    const heartbeatInterval = setInterval(() => {
      res.write("event: heartbeat\ndata: {}\n\n");
    }, 15000); // Send heartbeat every 15 seconds

    debuggerService.addClient(conversationId, res);

    req.on("close", () => {
      clearInterval(heartbeatInterval);
      debuggerService.removeClient(conversationId);
      res.end();
    });
  });

  return router;
}
