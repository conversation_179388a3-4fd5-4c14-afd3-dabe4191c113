import { Response } from "express";
import { logger } from "@neuratalk/common";
import { NLUResponse } from "../types/message.types";
import { ConversationContext } from "../types/conversation.types";

export enum DebuggerEventType {
  LOG = "log",
  NLU_LOG = "nlu_log",
  CONTEXT = "context",
}

export enum LogType {
  INFO = "info",
  WARNING = "warning",
  ERROR = "error",
}

export interface LogPayload {
  level: LogType;
  message: string;
  details?: any;
}

export type NluLogPayload = Omit<NLUResponse, "conversationId">;

export type ContextPayload = ConversationContext;

export type DebuggerEvent =
  | {
      type: DebuggerEventType.LOG;
      timestamp: string;
      conversationId: string;
      payload: LogPayload;
    }
  | {
      type: DebuggerEventType.NLU_LOG;
      timestamp: string;
      conversationId: string;
      payload: NluLogPayload;
    }
  | {
      type: DebuggerEventType.CONTEXT;
      timestamp: string;
      conversationId: string;
      payload: ContextPayload;
    };

export class DebuggerService {
  private static instance: DebuggerService;
  private clients: Map<string, Response | null> = new Map();

  private constructor() {
    logger.info("DebuggerService initialized as a singleton.");
  }

  public static getInstance(): DebuggerService {
    if (!DebuggerService.instance) {
      DebuggerService.instance = new DebuggerService();
    }
    return DebuggerService.instance;
  }

  public addClient(conversationId: string, res: Response): void {
    if (!this.clients.has(conversationId)) {
      this.clients.set(conversationId, res);
    }

    logger.debug(`Debugger client added for conversation ${conversationId}. `);

    res.on("close", () => {
      this.removeClient(conversationId);
      logger.debug(`Debugger client removed for conversation ${conversationId}. `);
    });
  }

  public removeClient(conversationId: string): void {
    this.clients.delete(conversationId);
  }

  public emit(event: DebuggerEvent): void {
    const data = `data: ${JSON.stringify(event)}\n\n`;

    const conversationClient = this.clients.get(event.conversationId);
    if (conversationClient) {
      try {
        conversationClient.write(data);
        conversationClient.flush();
      } catch (error) {
        logger.error(
          `Error writing to debugger client for conversation ${event.conversationId}:`,
          error,
        );
        this.removeClient(event.conversationId);
      }
    } else {
      logger.debug(
        `No debugger clients connected for conversation ${event.conversationId}, event emitted internally.`,
      );
    }
  }

  public log(level: LogType, message: string, conversationId: string, details?: any): void {
    this.emit({
      type: DebuggerEventType.LOG,
      timestamp: new Date().toISOString(),
      conversationId,
      payload: { level, message, details },
    });
  }

  public nluLog(nluDetails: NluLogPayload, conversationId: string): void {
    this.emit({
      type: DebuggerEventType.NLU_LOG,
      timestamp: new Date().toISOString(),
      conversationId,
      payload: nluDetails,
    });
  }

  public context(contextObject: ContextPayload, conversationId: string): void {
    this.emit({
      type: DebuggerEventType.CONTEXT,
      timestamp: new Date().toISOString(),
      conversationId,
      payload: contextObject,
    });
  }
}
