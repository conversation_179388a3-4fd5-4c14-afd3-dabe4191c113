import { DatabaseConnection } from "@neuratalk/bot-store";
import { RedisService } from "../services/redis.service";
import { NLUService } from "../services/nlu.service";
import { ApplicationRepository } from "../services/application-repository.service";
import { DebuggerService } from "../services/debugger.service";

export interface AppContext {
  db: DatabaseConnection;
  redis: RedisService;
  nlu: NLUService;
  applicationRepository: ApplicationRepository;
  debuggerService: DebuggerService;
}
