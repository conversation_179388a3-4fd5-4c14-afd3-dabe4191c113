import { Model, Op, UniqueConstraintError } from 'sequelize';
import { logger } from '@neuratalk/common'; // Assuming logger is available

interface UniqueNameScopedModel extends Model {
  name: string;
  botId: string;
  deletedAt?: Date | null; // Paranoid models have this
}

export async function validateUniqueNameScopedByBot(instance: UniqueNameScopedModel): Promise<void> {
  // const { name, botId, id } = instance;

  // Find an existing record with the same name and botId that is not soft-deleted
  // const existing = await (instance.constructor as any).findOne({
  //   where: {
  //     name: name,
  //     botId: botId,
  //     deletedAt: { [Op.eq]: null }, // Explicitly check for non-deleted records
  //     // Exclude the current instance if it's an update operation
  //     ...(id && { id: { [Op.ne]: id } }),
  //   },
  //   paranoid: false, // Important: search all records, including soft-deleted ones, then filter by deletedAt
  // });

  // if (existing) {
  //   logger.warn(`Attempted to create/update with duplicate name: '${name}' for bot '${botId}'`);
  //   throw new UniqueConstraintError({
  //     message: `An item with the name '${name}' already exists for this bot.`,
  //     fields: ['name', 'botId'],
  //     errors: [{
  //       message: `An item with the name '${name}' already exists for this bot.`,
  //       type: 'unique violation',
  //       path: 'name',
  //       value: name,
  //       origin: 'DB',
  //     }],
  //   });
  // }
}
