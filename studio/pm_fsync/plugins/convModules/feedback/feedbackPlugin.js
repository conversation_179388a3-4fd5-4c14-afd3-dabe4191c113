"use strict";
/**
 *  Feedback Plugin
 *
 *  <AUTHOR>
 **/
const ConvModulePlugin = require("../convModulePlugin");
const message = require("message");
const error_codes = message.error_codes;

let schema;

/**
 * @class FeedbackPlugin
 */
class FeedbackPlugin extends ConvModulePlugin {
  init() {}

  getMetaDataInfo() {
    return super.getMetaDataInfo(loadSchemaFile());
  }

  validate(module) {
    loadSchemaFile();
    return super.validate(module, schema);
  }

  exec(context) {
    return new Promise(async (resolve) => {
      try {
        // Initialize journey context
        this.updateJourneyContext(context);
        const { journeyContext, process } = context;

        // Handle user input if this is a resumed execution
        if (journeyContext?.userMessage) {
          this.handleUserInput(journeyContext);
        }

        const { globalContext, channelType, language } = journeyContext;
        const { sessionData ={} } = globalContext || {};
        const channelData = process.channelData[channelType] || process.channelData.web;
        const languageContent = channelData[language] || channelData.english;

        const feedbackId = process.feedbackId;
        const feedbackConfig = languageContent.feedbackConfig;
        const userInput = sessionData?.[feedbackId] || {};
       

        if (!Object.values(userInput).length) {
          // Need more input - pause execution
          const prompt = feedbackConfig.feedbackPrompt
          const formNodeData = {
            prompt,
            feedbackId,
            feedbackType: feedbackConfig.feedbackType,
          };
          const result = {
            code: error_codes.success,
            awaitingInput: true,
            nodeType: "feedback",
            data: formNodeData,
            journeyContext,
          };

          // Update journey context
          this.updateJourneyContext(context, {
            awaitingInput: true,
            currentForm: formNodeData,
          });

          resolve(result);
        } else {
          // All fields collected - form complete
          const result = {
            code: error_codes.success,
            data: {
              feedbackId: feedbackId,
              submittedData: userInput,
              submissionTime: new Date().toISOString(),
            },
          };

          // Update journey context
          this.updateJourneyContext(context, {
            awaitingInput: false,
            currentForm: null,
          });

          resolve(result);
        }
      } catch (error) {
        resolve({
          code: error_codes.pluginInternalError,
          msg: error.message || error,
        });
      }
    });
  }



  close() {}

  /**
   * Handle user input for resumed execution
   * @param {any} context Execution context
   * @param {any} userInput User input data
   */
  handleUserInput(journeyContext) {
    const { currentForm, awaitingInput, userMessage } = journeyContext;
    if (awaitingInput && currentForm) {
      let feedbackType = currentForm.feedbackType; 

      let userFeedbackData = {};

        // Validate user input
        const validationResult = this.validateFeedbackInput(userMessage.content, feedbackType);
        if (!validationResult.isValid) {
          throw new Error(`Validation failed: ${validationResult.error}`);
        }

        userFeedbackData = userMessage.formData;

      this.storeSessionData(journeyContext, currentForm.feedbackId, userFeedbackData);
    }
  }

  /**
   * Validate field input based on type and nodeData
   * @param {any} value User input value
   * @param {string} feedbackType Field type
   * @returns {object} Validation result
   */
  validateFeedbackInput(value, feedbackType) {
    switch (feedbackType) {
      case "Text":
        return this.validateTextStarInput(value);
      case "Star":
        return this.validateTextStarInput(value);
      case "Thumbs":
        return this.validateThumbsInput(value);
      default:
        return { isValid: true };
    }
  }

  validateTextStarInput(value) {
  // Check for non-string values
  if (typeof value !== "string") {
    return { isValid: false, error: "Must be text" };
  }

  // Check if value is a number between 1–5
  const num = Number(value);
  if (isNaN(num) || num < 1 || num > 5) {
    return { isValid: false, error: "Must be a number between 1 and 5" };
  }

  return { isValid: true };
}

 validateThumbsInput(value) {
  // Must be a string
  if (typeof value !== "string") {
    return { isValid: false, error: "Value must be a string" };
  }

  // Try to convert to number
  const num = Number(value);

  // Must be a number between 0 and 1 (inclusive)
  if (isNaN(num) || num < 0 || num > 1) {
    return { isValid: false, error: "Value must be a number between 0 and 1" };
  }

  return { isValid: true };
}
}

module.exports = FeedbackPlugin;

function loadSchemaFile() {
  if (schema) {
    return schema;
  }
  schema = require("./feedbackSchema.json");
  schema.category = "feedback";
  return schema;
}