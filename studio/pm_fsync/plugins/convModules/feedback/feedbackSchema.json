{"typeId": "2.4", "name": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON> Handler", "description": "This module is used to handle feedback forms", "type": "object", "required": ["name", "coordinates", "settings", "process", "output"], "properties": {"name": {"description": "Name Of the Module", "title": "Module name", "type": "string", "minLength": 1}, "coordinates": {"description": "Coordinates Of the Module", "title": "Coordinates", "type": "object", "properties": {"id": {"description": "Coordinate ID", "title": "Coordinate ID", "type": "string", "minLength": 1}, "type": {"description": "Coordinate type", "title": "Coordinate Type", "type": "string", "minLength": 1}, "ports": {"description": "Coordinate ports", "title": "Coordinate ports", "type": "array", "minItems": 1}, "nodedata": {"description": "Node data", "title": "Node data", "type": "object", "properties": {"title": {"description": "The title of node", "title": "Module title", "type": "string"}, "name": {"description": "The name of node", "title": "Module Name", "type": "string"}, "id": {"description": "The Id of node", "title": "Module ID", "type": "string"}}}}}, "settings": {"description": "Setting properties Of the Module", "title": "Settings", "type": "object", "properties": {"enableLogging": {"description": "Enable message logging", "title": "Enable Logging", "type": "boolean", "default": true}, "validateRequired": {"description": "Validate required fields", "title": "Validate Required Fields", "type": "boolean", "default": true}}}, "input": {"description": "Input parameters", "title": "Input", "type": "object"}, "process": {"type": "object", "required": ["feedbackId", "channelData"], "properties": {"feedbackId": {"type": "string", "minLength": 1, "description": "Unique identifier for the feedback"}, "channelData": {"type": "object", "properties": {"web": {"type": "object", "properties": {"english": {"$ref": "#/definitions/feedbackContent"}, "hindi": {"$ref": "#/definitions/feedbackContent"}, "german": {"$ref": "#/definitions/feedbackContent"}, "arabic": {"$ref": "#/definitions/feedbackContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}, "whatsapp": {"type": "object", "properties": {"english": {"$ref": "#/definitions/feedbackContent"}, "hindi": {"$ref": "#/definitions/feedbackContent"}, "german": {"$ref": "#/definitions/feedbackContent"}, "arabic": {"$ref": "#/definitions/feedbackContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}, "mobile": {"type": "object", "properties": {"english": {"$ref": "#/definitions/feedbackContent"}, "hindi": {"$ref": "#/definitions/feedbackContent"}, "german": {"$ref": "#/definitions/feedbackContent"}, "arabic": {"$ref": "#/definitions/feedbackContent"}}, "required": ["english", "hindi", "german", "arabic"], "additionalProperties": false}}, "required": ["web", "whatsapp", "mobile"], "additionalProperties": false}}, "additionalProperties": false}, "output": {"description": "The output params", "type": "object"}, "definitions": {"feedbackContent": {"type": "object", "properties": {"feedbackConfig": {"type": "object", "required": ["feedbackPrompt", "feedbackType"], "properties": {"feedbackType": {"title": "Feedback Type", "type": "string", "enum": ["star", "text", "thumbs"]}, "feedbackPrompt": {"title": "Feedback Prompt", "type": "string", "description": "The prompt for feedback"}}}}, "required": ["feedbackConfig"], "additionalProperties": false}}}}