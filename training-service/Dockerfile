# ---- Builder Stage ----
    FROM python:3.10-slim

    WORKDIR /app
    
    ARG PROXY_URL
    
    ENV http_proxy=$PROXY_URL
    ENV https_proxy=$PROXY_URL
    
    # Install build dependencies
    RUN apt-get update && apt-get install -y --no-install-recommends build-essential
    
    # # Set up a virtual environment
    # ENV VIRTUAL_ENV=/opt/venv
    # RUN python -m venv $VIRTUAL_ENV
    # ENV PATH="$VIRTUAL_ENV/bin:$PATH"
    
    # Install dependencies
    COPY training-service/requirements.txt .
    RUN pip install --no-cache-dir -r requirements.txt
    
    
    COPY training-service/src ./src
    
    ENV http_proxy=""
    ENV https_proxy=""
    
    CMD ["python", "src/main.py"]
    